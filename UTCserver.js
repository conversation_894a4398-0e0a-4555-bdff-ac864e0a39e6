/*
	版本号V4~10
	该接口库负责中间件主体功能和通用接口功能
	可调用接口：
		初始化
		获取钥匙信息
	示例：
		UTCserver.Initialize({
			path:'file/优泰签章.exe',
			evaluate:'js/UTCserve_evaluate.js',
			success:function(version){},
			fail:function(message){}
		});
	版本命名规范：V版本-版本分支~版本号
*/
function UTCserver_Function(){
	var self=this;
	self.versionnum='1.1.0.2';
	self.loadingflag=null;
	self.usekey=false;
	self.vnum=null;
	self.initsure=true;
	self.errorstate={
		//通用
		x000000:'中间件运行异常，请运行桌面上的中间件恢复进行',
		x000001:'中间件不是最新版本，请安装最新版本的中间件',
		x000003:'参数错误',
		x000004:'处理请求超时',
		x000005:'服务器连接失败',
		x000006:'身份证组件加载失败，请重新安装'
	};
	self.init=function(){
		var browser=navigator.userAgent;
		var version=browser.split(' ');
		function typearray(ele){
			var type=false;
			var num;
			for(var i=0;i<version.length;i++){
				var str=version[i];
				if(RegExp(ele).test(str)){
					type=true;
					num=i;
				};
			};
			return{
				type:type,
				num:num
			};
		};
		if(typearray('MSIE').type){
			var num=typearray('MSIE').num;
			var onum = /^\d+\.\d+/;
			var vnum=onum.exec(version[num+1].replace(/[^0-9.]/ig,""));
			self.vnum = vnum; 
			if(self.vnum<8){
				alert('请使用IE8及以上内核的浏览器或非IE内核浏览器');
			};
		};
		self.Plugin=new UTCserver_Plugin();
		self.UIloading=new UTCserver_Loading();
		if (!("classList" in document.documentElement)&&typeof(HTMLElement)!='undefined') {
		    Object.defineProperty(HTMLElement.prototype, 'classList', {
		        get: function() {
		            var self = this;
		            function update(fn) {
		                return function(value) {
		                    var classes = self.className.split(/\s+/g),
		                        index = classes.indexOf(value);
		                    
		                    fn(classes, index, value);
		                    self.className = classes.join(" ");
		                }
		            }
		            
		            return {                    
		                add: update(function(classes, index, value) {
		                    if (!~index) classes.push(value);
		                }),
		                
		                remove: update(function(classes, index) {
		                    if (~index) classes.splice(index, 1);
		                }),
		                
		                toggle: update(function(classes, index, value) {
		                    if (~index)
		                        classes.splice(index, 1);
		                    else
		                        classes.push(value);
		                }),
		                
		                contains: function(value) {
		                    return !!~self.className.split(/\s+/g).indexOf(value);
		                },
		                
		                item: function(i) {
		                    return self.className.split(/\s+/g)[i] || null;
		                }
		            };
		        }
		    });
		};
		if (!Array.prototype.indexOf){
		  Array.prototype.indexOf = function(elt /*, from*/){
		    var len = this.length >>> 0;
		
		    var from = Number(arguments[1]) || 0;
		    from = (from < 0)
		         ? Math.ceil(from)
		         : Math.floor(from);
		    if (from < 0)
		      from += len;
		
		    for (; from < len; from++){
		      if (from in this && this[from] === elt)
		        return from;
		    }
		    return -1;
		  };
		}
		var csscon=document.createElement("style");
		csscon.type='text/css'; 
		var stylecon='.utc_popupbox{ position:fixed; top:0; left:0; width:100%; height:100%; background-color:rgba(0,0,0,0.4); opacity: 0; z-index: 100;transition: none;-moz-transition: none;-webkit-transition: none;-o-transition: none;}.utc_popupbox *{ margin: 0; padding: 0; color: #333333;}.utc_popupbox ol,.utc_popupbox ul{list-style:none; overflow: hidden;}.utc_popupbox li{ float: left;}.utc_popupbox img{ border: none;}.utc_popupbox input,.utc_popupbox button,.utc_popupbox textarea{ border: none; background-color: transparent; vertical-align:middle; outline: none; overflow: visible;}.utc_popupbox .utc_popupbg{ width: 100%; height: 100%; background-color: #000000; background-color: rgba(0,0,0,0); filter:alpha(opacity=40);}.utc_popupbox .utc_popup{ position: absolute; top: 0; left: 0; bottom: 0; right: 0; margin: auto; background-color: #FFFFFF; overflow: auto;}.utc_popupbox .utc_title{ height: 40px; line-height: 40px; padding: 0 20px; font-size: 16px;}.utc_popupbox .utc_con{ margin: 0 20px; overflow: auto;}.utc_popupbox .utc_btn{ height: 50px;}.utc_popupbox .utc_btn button{ float: right; height: 30px; margin: 10px; padding: 0 20px; font-size: 14px; background-color: transparent; border-radius: 2px; cursor: pointer;}.utc_popupbox .utc_btn button.close:hover{ background-color: #F2F2F2;}.utc_popupbox .utc_btn button.sure{ color: #FFFFFF; background-color: #00C0A5;}.utc_popupbox .utc_btn button.sure.none{ color: #FFFFFF; background-color: #CCCCCC; cursor: default;}object{ display: none;}';
	    stylecon+='.utc_popupbox.loading .utc_popup{ width: 200px; height: 130px;}.utc_popupbox.loading .utc_popup img{ display: block; margin: 0 auto; margin-top: 30px;}.utc_popupbox.loading .utc_popup .hint{ height: 30px; line-height: 30px; text-align: center;}';
	    if(csscon.styleSheet){         //ie下
	    	csscon.styleSheet.cssText = stylecon;  
	    } else {  
	    	csscon.innerHTML = stylecon;       //或者写成 nod.appendChild(document.createTextNode(str))  
	    }  
		document.getElementsByTagName('head')[0].appendChild(csscon);
	};
	//初始化
	self.Initialize=function(json){
		self.openloading();
		if(self.initsure){
			self.init();
			self.Plugin.PluginVersion(function(state,tmp){
				if(state=='success'){
					self.initsure=false;
					self.closeloading();
					self.version=tmp;
					json.success(self.version);
				};
				self.errorcallback(json,state,tmp);
			});
			//加载外挂接口库
			if(json.inside){
				self.loadjs(json.inside,function(){
					self.Inside=new UTCserver_Inside();
				});
			};
			if(json.evaluate){
				self.loadjs(json.evaluate,function(){
					self.Evaluate=new UTCserver_Evaluate();
				});
			};
			if(json.idcard){
				self.loadjs(json.idcard,function(){
					self.Idcard=new UTCserver_Idcard();
				});
			};
		}else{
			self.closeloading();
			json.success(self.version);
		};
	};
	
	//获取钥匙信息
	self.GetKeyInfo=function(json){
		self.Initialize({
			success:function(version){
				self.openloading();
				if(json.envelope==true){
					self.GetKeyInfo.prototype.EnvelopedId(json);
				}else{
					self.GetKeyInfo.prototype.Id(json);
				};
			},
			fail:function(message){
				self.errorcallback(json,'fail',message);
			},
			error:function(message){
				self.errorcallback(json,'error',message);
			}
		});
	};
	self.GetKeyInfo.prototype.Id=function(json){//序列号
		self.Plugin.GetAEKeyID(function(state,id){
			if(state=='success'&&id==''){
				state='fail';
				id='没有检测到钥匙';
			}
			if(state=='success'){
				self.closeloading();
				switch(json.content){
					case 'id' :
					json.success(id);
					break;
					default:
					json.success({id:id});
				};
			};
			self.errorcallback(json,state,id);
		});
	};
	self.GetKeyInfo.prototype.EnvelopedId=function(json){//加密序列号
		self.Plugin.GetAEKeyEnvelopedID(function(state,id){
			if(state=='success'&&id==''){
				state='fail';
				id='没有检测到钥匙';
			}
			if(state=='success'){
				self.closeloading();
				switch(json.content){
					case 'id' :
					json.success(id);
					break;
					default:
					json.success({id:id});
				};
			};
			self.errorcallback(json,state,id);
		});
	};	
	
	//检测钥匙Pin码
	self.OpenKey=function(json){
		self.Initialize({
			success:function(version){
				self.openloading();
				self.Plugin.OpenEKey(json.pin,function(state,pin){
					if(state=='success'){
						self.closeloading();
						json.success(pin);
					};
					self.errorcallback(json,state,pin);
				});
			},
			fail:function(message){
				self.errorcallback(json,'fail',message);
			},
			error:function(message){
				self.errorcallback(json,'error',message);
			}
		});
	};
	//通用参数
	self.notdn=function(data,callback){
		if(typeof(data)=='undefined'){
			self.UIcertlist.get({},function(state,data){
				self.openloading();
				callback(state,data);
			});
		}else{
			callback('success',data);
		};
	};
	self.notpath=function(data,callback){
		if(typeof(data)=='undefined'){
			self.Plugin.GetLocalFilePath('*',function(state,path){
				self.openloading();
				callback(state,path);
			});
		}else{
			callback('success',data);
		};
	};
	
	//判断浏览器
	self.Browser=function(){
		var ele=navigator.userAgent;
		if(ele.indexOf('Firefox')>=0){
			return 'Firefox';
		}else{
			if(ele.indexOf('Edge')>=0){
				return 'Edge';
			}else{
				if(ele.indexOf('Trident')>=0){
					return 'IE';
				}else{
					if(ele.indexOf('Opera')>=0){
						return 'Opera';
					}else{
						if(ele.indexOf('Chrome')>=0){
							return 'Chrome';
						}else{
							if(ele.indexOf('Safari')>=0){
								return 'Safari';
							}else{
								return 'Other';
							};
						};
					};
				};
			};
		};
	};
	
	//错误回调
	self.errorcallback=function(json,state,data){
		if(state=='fail'){
			self.closeloading();
			if(typeof(json.fail)=='function'){
				json.fail(data);
			}else{
				alert(data);
			};
		};
		if(state=='error'){
			self.closeloading();
			if(typeof(json.error)=='function'){
				json.error(data);
			}else{
				alert(eval('self.errorstate.'+data));
			};
		};
	};
	
	//随机数
	self.randomnum=function(a){
		var num='';
		for(var i=1;i<=a;i++){
			//var chars = ['0','1','2','3','4','5','6','7','8','9','A','B','C','D','E','F','G','H','I','J','K','L','M','N','O','P','Q','R','S','T','U','V','W','X','Y','Z','a','b','c','d','e','f','g','h','i','j','k','l','m','n','o','p','q','r','s','t','u','v','w','x','y','z'];
			var chars = ['0','1','2','3','4','5','6','7','8','9','A','B','C','D','E','F'];
      var o = Math.floor(Math.random()*16);
			num+=chars[o];
		};
		return num;
	};
	//打开loading
	self.openloading=function(){
		self.loadingflag=setTimeout(function(){self.UIloading.get();},1000);
	};
	//关闭loading
	self.closeloading=function(){
		clearTimeout(self.loadingflag);
		self.UIloading.close();
	};
	//加载文件
	self.loadcss=function(path,callback){
		if(!path || path.length === 0){
			throw new Error('argument "path" is required !');
		}
		var pathsure=true;
		for(var i=0;i<document.querySelectorAll('script').length;i++){
			if(document.querySelectorAll('script')[i].src.indexOf(path)>=0){
				pathsure=false;
			};
		};
		if(pathsure){
			var head = document.getElementsByTagName('head')[0];
	        var link = document.createElement('link');
	        link.href = path;
	        link.rel = 'stylesheet';
	        link.type = 'text/css';
	        head.appendChild(link);
	        script.onload=script.onreadystatechange=function(){
			   if(!this.readyState||this.readyState=='loaded'||this.readyState=='complete'){
			     callback();
			   }
			   script.onload=script.onreadystatechange=null;
			};
		}else{
			callback();
		};
    };
    self.loadjs=function(path,callback){
		if(!path || path.length === 0){
			throw new Error('argument "path" is required !');
		}
		var pathsure=true;
		for(var i=0;i<document.querySelectorAll('script').length;i++){
			if(document.querySelectorAll('script')[i].src.indexOf(path)>=0){
				pathsure=false;
			};
		};
		if(pathsure){
			var head = document.getElementsByTagName('head')[0];
	        var script = document.createElement('script');
	        script.src = path;
	        script.type = 'text/javascript';
	        head.appendChild(script);
	        script.onload=script.onreadystatechange=function(){
			   if(!this.readyState||this.readyState=='loaded'||this.readyState=='complete'){
			     callback();
			   }
			   script.onload=script.onreadystatechange=null;
			};
		}else{
			callback();
		};
    };
	//请求
	self.UTCajax=function(json,callback){
		if(true){
			try{
				eval("json = '"+JSON.stringify(json)+"';");
				//setTimeout(function(){
					var url = 'http://127.0.0.1:58901/utcapi';
					if(document.location.protocol=='https:'&&self.Browser()!='Chrome'){
						url = 'https://127.0.0.1:58911/utcapi';
					};
					if(self.vnum!=null&&self.vnum!=undefined&&self.vnum<=9){
						 var xdr = new XDomainRequest();
						 xdr.onload = function() {      
							//var obj = eval( "("+response.responseText+")" );
							var obj = Ext.util.JSON.decode(response.responseText);
							 if(obj.error){
								callback('fail',obj.return_val);
							 }else{
								callback('success',obj.return_val);
							};
						 };
						 xdr.onerror = function() {
							 callback('error','x000000');
						 };
						 xdr.ontimeout = function() {
							 callback('error','x000000');
						 };
						 xdr.open("post", url);
						 xdr.send('json=' + json);
					}else{
							var request = new XMLHttpRequest();
							request.open('POST', url, false);
							request.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded; charset=UTF-8');
							request.onreadystatechange= function(){
								if(request.readyState == 4) {
									if(request.status == 200) {
										//var obj = eval( "("+response.responseText+")" );
										var obj = Ext.util.JSON.decode(response.responseText);
										if(obj.error){
											callback('fail',obj.return_val);
										}else{
											callback('success',obj.return_val);
										};
									}else{
										callback('error','x000000');
									};
								};
							};
							json=json.replace(/\\/ig,"\\\\"); 
							eval("json = '"+json+"';");
							//console.log(json);
							request.send('json=' + json);
					};
				//},0);
			}catch(e){
				//console.log(e.message);
				callback('error','x000000');
			};
		};
	};
	//外部请求
	self.WEBajax=function(json,callback){
		if(true){
			try{
				var url=json.url;
				setTimeout(function(){
					var request = new XMLHttpRequest();
					request.open('POST', url, false);
					request.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded;charset=utf-8');
					request.onreadystatechange= function(){
						if(request.readyState == 4) {
							if(request.status == 200) {
								//var obj = eval( "("+response.responseText+")" );
							    var obj = Ext.util.JSON.decode(response.responseText);
								callback('success',obj);
							}else{
								callback('error','x000005');
							};
						};
					};
					json = (function(obj){ // 转成post需要的字符串.
					    var str = "";
					    for(var prop in obj){
					        str += prop + "=" + obj[prop] + "&"
					    };
					    str=str.substring(0,str.length-1);
					    return str;
					})(json);
					request.send(json);
				},0);
			}catch(e){
				//console.log(e.message);
				callback('error','x000005');
			};
		};
	};
	self.endWith=function(path,s){
		if(s==null||s==""||path.length==0||s.length>path.length){
			return false;
		}else{
			if(path.substring(path.length-s.length)==s){
				return true;
			}else{
				return false;
			};
		};
	};
};
var UTCserver=new UTCserver_Function();

function UTCserver_Plugin(){
	var self=this;
	//获取服务版本号
	self.PluginVersion=function(callback){
		UTCserver.UTCajax({
			'function':'PluginVersion'
		},function(state,data){
			callback(state,data);
		});
	};
	//获取钥匙序列号
	self.GetAEKeyID=function(callback){
		UTCserver.UTCajax({
			'function':'GetAEKeyID'
		},function(state,data){
			callback(state,data);
		});
	};
	//获取加密钥匙序列号
	self.GetAEKeyEnvelopedID=function(callback){
		UTCserver.UTCajax({
			'function':'GetAEKeyEnvelopedID'
		},function(state,data){
			callback(state,data);
		});
	};
	//检测钥匙PIN码
	self.OpenEKey=function(psw,callback){
		UTCserver.UTCajax({
			'function':'OpenEKey',
			'1':psw
		},function(state,data){
			callback(state,data);
		});
	};
};

//loading
function UTCserver_Loading(){
	var self=this;
	self.ran=UTCserver.randomnum(4);
	self.initialize=function(){
	};
	self.close=function(){
		self.fadeOut(document.querySelector('.utc_popupbox.loading.ran'+self.ran),function(){
			document.querySelector('.utc_popupbox.loading.ran'+self.ran).parentNode.removeChild(document.querySelector('.utc_popupbox.loading.ran'+self.ran));
		});
	};
	self.get=function(){
		var node=document.createElement("div");
		self.addClass(node,'utc_popupbox');
		self.addClass(node,'loading');
		self.addClass(node,'ran'+self.ran);
		var divbox='<div class="utc_popupbg"></div><div class="utc_popup"><img src="data:image/gif;base64,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"><div class="hint">正在加载</div></div>';
		node.innerHTML=divbox;
		if(document.querySelectorAll('.utc_popupbox.loading.ran'+self.ran).length==0){
			document.body.appendChild(node);
			self.fadeIn(document.querySelector('.utc_popupbox.loading.ran'+self.ran));
		};
	};
	self.hasClass=function(obj, cls) {
		return obj.className.match(new RegExp('(\\s|^)' + cls + '(\\s|$)'));  
	};
	self.addClass=function(obj, cls) {
	    if (!self.hasClass(obj, cls)) {
		    obj.className += " " + cls;
	    };
	};
	self.removeClass=function(obj, cls) {
	    if (self.hasClass(obj, cls)) {
	        var reg = new RegExp('(\\s|^)' + cls + '(\\s|$)');
	        obj.className = obj.className.replace(reg, ' ');
	    };
	};
	self.fadeIn=function(el,callback){
		try{
		  el.style.opacity = 0;
		
		  var last = +new Date();
		  var tick = function() {
		    el.style.opacity = +el.style.opacity + (new Date() - last) / 400;
		    last = +new Date();
		
		    if (+el.style.opacity < 1) {
		      (window.requestAnimationFrame && requestAnimationFrame(tick)) || setTimeout(tick, 16)
		    }else{
		    	try{callback();}catch(e){};
		    }
		  };
		
		  tick()
		}catch(e){};
	};
	self.fadeOut=function(el,callback){
		try{
		  el.style.opacity = 1;
		
		  var last = new Date();
		  var tick = function() {
		    el.style.opacity = el.style.opacity - (new Date() - last) / 400;
		    last = new Date();
		
		    if (el.style.opacity > 0) {
		      (window.requestAnimationFrame && requestAnimationFrame(tick)) || setTimeout(tick, 16)
		    }else{
		    	try{callback();}catch(e){};
		    }
		  };
		
		  tick()
		}catch(e){};
	};
	self.initialize();
};
