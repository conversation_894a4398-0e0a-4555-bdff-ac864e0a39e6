//设置用户名
function setCookieCode(){
    var expdate = new Date();
    expdate.setTime (expdate.getTime() + 10000 * (365*24 * 60 * 60 * 1000));
    SetCookie("adCode",document.getElementById("adCode").value,expdate);
}
// 保存密码
function setCookiePwd(){
    var expdate = new Date();
    expdate.setTime (expdate.getTime() + 10000 * (365*24 * 60 * 60 * 1000));
    SetCookie("pwd",document.getElementById("pwd").value,expdate);
}
//创建指定名称的Cookie
function SetCookie (name, value) {
    try{
        var argv = SetCookie.arguments;
        var argc = SetCookie.arguments.length;
        var expires = (argc > 2) ? argv[2] : null;
        var path = (argc > 3) ? argv[3] : null;
        var domain = (argc > 4) ? argv[4] : null;
        var secure = (argc > 5) ? argv[5] : false;
        document.cookie = name + "=" + escape (value) +
            ((expires == null) ? "" : ("; expires=" + expires.toGMTString())) +
            ((path == null) ? "" : ("; path=" + path)) +
            ((domain == null) ? "" : ("; domain=" + domain)) +
            ((secure == true) ? "; secure" : "");
    }catch(e){
        alert(e.message);
    }
}


//获取指定Cookie名称的Cookie值
function GetCookie (name) {
    var arg = name + "=";
    var alen = arg.length;
    var clen = document.cookie.length;
    var i = 0;
    while (i < clen) {
        var j = i + alen;
        if (document.cookie.substring(i, j) == arg)
            return getCookieVal (j);
        i = document.cookie.indexOf(" ", i) + 1;
        if (i == 0) break;
    }
    return null;
}

function getCookieVal (offset) {
    var endstr = document.cookie.indexOf (";", offset);
    if (endstr == -1)
        endstr = document.cookie.length;
    return unescape(document.cookie.substring(offset, endstr));
}


//删除指定名称的cookie
function delCookie(name){
    var exp = new Date();
    exp.setTime(exp.getTime() - 1);
    var cval=GetCookie(name);
    if(cval!=null) document.cookie= name + "="+cval+";expires="+exp.toGMTString();
}

// 自动填充用户
function AutoLogin(){
    if((GetCookie ("adCode")!=null)&&(GetCookie ("adCode")!="")){
        document.getElementById("adCode").value = GetCookie ("adCode");
        document.getElementById("saveAdCode").checked = true;
    }
    if((GetCookie ("pwd")!=null) &&(GetCookie ("pwd")!="")){
        document.getElementById("pwd").value= GetCookie ("pwd");
        document.getElementById("savePwd").checked = true;
    }
}



function imageSubmit() {

    if (document.getElementById("saveAdCode").checked) {
        setCookieCode();
    } else {
        delCookie("adCode");
    }
    if (document.getElementById("savePwd").checked) {
        setCookiePwd();
    } else {
        delCookie("pwd");
    }
}


$(function(){

    AutoLogin();
})
