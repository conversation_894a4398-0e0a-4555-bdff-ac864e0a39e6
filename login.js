function doCipherSubmit(isEncryptTrans,exponent,modulus,encryptedAdCode,dontSave){
    if("true"!=dontSave)  {
        imageSubmit();
    }
    if( document.getElementById("adCode").value==undefined||document.getElementById("adCode").value==""||document.getElementById("adCode").value==null){
        alert("用户名不能为空！");
        return  false ;
    }
    if (!document.getElementById("dlsf") || document.getElementById("dlsf").value==1) {
        if (document.getElementById("pwd").value == undefined || document.getElementById("pwd").value == "" || document.getElementById("pwd").value == null) {
            alert("密码不能为空！");
            return false;
        }
    }
    var isEncryptTrans =isEncryptTrans;
    if(isEncryptTrans){
        var key =RSAUtils.getKeyPair(exponent,'',modulus)
        var encryptedAdCode =encryptedAdCode;
        doEncryptTrans(key, encryptedAdCode);
    }
    document.UALoginForm.submit();
}


function doEncryptTrans(e,t){
    var n=document.getElementById("adCode").value;
    document.getElementById(t).value=RSAUtils.encryptedString(e,n);
    var d=document.getElementById("pwd").value;
    // var a="_"+(new Date).getTime().toString();
    if(d.length<256){
        document.getElementById("pwd").value=RSAUtils.encryptedString(e, d);
    }else{
        document.getElementById("pwd").value=d;
    }

    // document.getElementById("pwd").value=RSAUtils.encryptedString(e,sha256_digest(d)+"_"+d);
    //  document.getElementById("saltPwd").value=RSAUtils.encryptedString(e,sha256_digest(d+a)+a)
    var tsl=(new Date).getTime().toString();
    document.getElementById("tsl").value=RSAUtils.encryptedString(e,tsl);
    var nonce=getNonceString();
    document.getElementById("nonce").value=RSAUtils.encryptedString(e,nonce);
}

/**
 * 返回长度为12的随机数字窜
 * */
function getNonceString (){
    var nonce ="";
    for(var i=0;i<12;i++) {
        nonce += Math.floor(Math.random()*10);
    }
    return nonce;
}
