function rotateRight(a,r){return r>>>a|r<<32-a}function choice(a,r,h){return a&r^~a&h}function majority(a,r,h){return a&r^a&h^r&h}function sha256_Sigma0(a){return rotateRight(2,a)^rotateRight(13,a)^rotateRight(22,a)}function sha256_Sigma1(a){return rotateRight(6,a)^rotateRight(11,a)^rotateRight(25,a)}function sha256_sigma0(a){return rotateRight(7,a)^rotateRight(18,a)^a>>>3}function sha256_sigma1(a){return rotateRight(17,a)^rotateRight(19,a)^a>>>10}function sha256_expand(a,r){return a[r&15]+=sha256_sigma1(a[r+14&15])+a[r+9&15]+sha256_sigma0(a[r+1&15])}var K256=new Array(1116352408,1899447441,3049323471,3921009573,961987163,1508970993,2453635748,2870763221,3624381080,310598401,607225278,1426881987,1925078388,2162078206,2614888103,3248222580,3835390401,4022224774,264347078,604807628,770255983,1249150122,1555081692,1996064986,2554220882,2821834349,2952996808,3210313671,3336571891,3584528711,113926993,338241895,666307205,773529912,1294757372,1396182291,1695183700,1986661051,2177026350,2456956037,2730485921,2820302411,3259730800,3345764771,3516065817,3600352804,4094571909,275423344,430227734,506948616,659060556,883997877,958139571,1322822218,1537002063,1747873779,1955562222,2024104815,2227730452,2361852424,2428436474,2756734187,3204031479,3329325298);var ihash,count,buffer;var sha256_hex_digits="0123456789abcdef";function safe_add(a,r){var h=(a&65535)+(r&65535);var t=(a>>16)+(r>>16)+(h>>16);return t<<16|h&65535}function sha256_init(){ihash=new Array(8);count=new Array(2);buffer=new Array(64);count[0]=count[1]=0;ihash[0]=1779033703;ihash[1]=3144134277;ihash[2]=1013904242;ihash[3]=2773480762;ihash[4]=1359893119;ihash[5]=2600822924;ihash[6]=528734635;ihash[7]=1541459225}function sha256_transform(){var a,r,h,t,e,i,f,n,s,o;var u=new Array(16);a=ihash[0];r=ihash[1];h=ihash[2];t=ihash[3];e=ihash[4];i=ihash[5];f=ihash[6];n=ihash[7];for(var c=0;c<16;c++)u[c]=buffer[(c<<2)+3]|buffer[(c<<2)+2]<<8|buffer[(c<<2)+1]<<16|buffer[c<<2]<<24;for(var _=0;_<64;_++){s=n+sha256_Sigma1(e)+choice(e,i,f)+K256[_];if(_<16)s+=u[_];else s+=sha256_expand(u,_);o=sha256_Sigma0(a)+majority(a,r,h);n=f;f=i;i=e;e=safe_add(t,s);t=h;h=r;r=a;a=safe_add(s,o)}ihash[0]+=a;ihash[1]+=r;ihash[2]+=h;ihash[3]+=t;ihash[4]+=e;ihash[5]+=i;ihash[6]+=f;ihash[7]+=n}function sha256_update(a,r){var h,t,e=0;t=count[0]>>3&63;var i=r&63;if((count[0]+=r<<3)<r<<3)count[1]++;count[1]+=r>>29;for(h=0;h+63<r;h+=64){for(var f=t;f<64;f++)buffer[f]=a.charCodeAt(e++);sha256_transform();t=0}for(var f=0;f<i;f++)buffer[f]=a.charCodeAt(e++)}function sha256_final(){var a=count[0]>>3&63;buffer[a++]=128;if(a<=56){for(var r=a;r<56;r++)buffer[r]=0}else{for(var r=a;r<64;r++)buffer[r]=0;sha256_transform();for(var r=0;r<56;r++)buffer[r]=0}buffer[56]=count[1]>>>24&255;buffer[57]=count[1]>>>16&255;buffer[58]=count[1]>>>8&255;buffer[59]=count[1]&255;buffer[60]=count[0]>>>24&255;buffer[61]=count[0]>>>16&255;buffer[62]=count[0]>>>8&255;buffer[63]=count[0]&255;sha256_transform()}function sha256_encode_bytes(){var a=0;var r=new Array(32);for(var h=0;h<8;h++){r[a++]=ihash[h]>>>24&255;r[a++]=ihash[h]>>>16&255;r[a++]=ihash[h]>>>8&255;r[a++]=ihash[h]&255}return r}function sha256_encode_hex(){var a=new String;for(var r=0;r<8;r++){for(var h=28;h>=0;h-=4)a+=sha256_hex_digits.charAt(ihash[r]>>>h&15)}return a}function sha256_digest(a){sha256_init();sha256_update(a,a.length);sha256_final();return sha256_encode_hex()}function sha256_self_test(){return sha256_digest("message digest")=="f7846f55cf23e14eebeab5b4e1550cad5b509e3348fbc4efa3a1413d393cb650"}