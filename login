








<html>
<head>
	<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
	<meta http-equiv="Pragma" content="no-cache" />
	<meta http-equiv="Cache-Control" content="no-cache" />
	<meta http-equiv="Expires" content="0" />
	<!--必须项 begin -->
	<title>s</title>
	<link rel="stylesheet" href="layui/css/layui.css">
	<link rel="stylesheet" href="fingerPrint/css/box.css" type="text/css">
	<!--必须项 end -->
	<style type="text/css">
		<!--
		body {
			background-color: #FFFFFF;
		}

		.input1 {
			height: 20px;
			width: 110px;
			font-size: 9pt;
		}

		.STYLE1 {
			color: #9DD4D6
		}

		.imgp {
			padding-bottom: 8px;
		}
		a:link {
			color: #9AD3D3;
			font-size: 9pt;
		}

		a:visited {
			color: #9AD3D3;
			font-size: 9pt;
		}

		a:hover {
			color: #9CD4D4;
			font-size: 9pt;
		}
		a:active {
			color: #99D3D3;
			font-size: 9pt;
		}


		.title{
			position: absolute;
			top: 260px;
			left: 650px;
		}
		.title .text{
			font-size:9px;
			font-family: '华文中宋';
			color: #006f68;
			height: 5px;
			line-height: 5px;
			letter-spacing: 2px;
		}
		.title .spell{
			font-size:12px;
			font-family: 'Adobe 仿宋 Std';
			color: #338d89;
			height: 5px;
			line-height: 5px;
			letter-spacing: 0.4px;
		}
		table {
			border-collapse: collapse;
			border-spacing: 0;
			margin: 0px 0px 8px 0;
		}


        -->
    </style>
	<script type="text/javascript"
			src="/UALogin/js/jqueryMin.js">
	</script>
	<script type="text/javascript">
        function checkPINFn(){
            UTCserver.Initialize({
                success:function(version){

                },
                fail:function(message){

                }
            });
            UTCserver.GetKeyInfo({
                content:'id',
                envelope:true,
                pin:'111111',
                success:function(id){

                },
                fail:function(message){
                    window.location.href = '/UALogin/error.jsp?msg=' + message;
                }
            });
        }

        function clear(){
            if(document.forms[0].adCode.value.length == 256){
                document.forms[0].adCode.value="";
                document.forms[0].pwd.value="";
            }
        }

        function initPageFn(){
            try{
                var isCheckPIN = false;
                if(isCheckPIN){
                    checkPINFn();
                }

                var isEncryptTrans =true
                if(isEncryptTrans){
                    var t =setTimeout("clear();",10);
                }
                document.forms[0].adCode.focus();
				// 初始化ukey列表
				if(false){
					SetUserCertList("certField");
				}
            }catch(ex){
            }
        }


    //设置用户名
    function setCookieCode(){
        var expdate = new Date();
        expdate.setTime (expdate.getTime() + 10000 * (365*24 * 60 * 60 * 1000));
        SetCookie("adCode",document.getElementById("adCode").value,expdate);
    }
    // 保存密码
    function setCookiePwd(){
        var expdate = new Date();
        expdate.setTime (expdate.getTime() + 10000 * (365*24 * 60 * 60 * 1000));
        SetCookie("pwd",document.getElementById("pwd").value,expdate);
    }
    //创建指定名称的Cookie
    function SetCookie (name, value) {
        try{
            var argv = SetCookie.arguments;
            var argc = SetCookie.arguments.length;
            var expires = (argc > 2) ? argv[2] : null;
            var path = (argc > 3) ? argv[3] : null;
            var domain = (argc > 4) ? argv[4] : null;
            var secure = (argc > 5) ? argv[5] : false;
            document.cookie = name + "=" + escape (value) +
                ((expires == null) ? "" : ("; expires=" + expires.toGMTString())) +
                ((path == null) ? "" : ("; path=" + path)) +
                ((domain == null) ? "" : ("; domain=" + domain)) +
                ((secure == true) ? "; secure" : "");
        }catch(e){
            alert(e.message);
        }
    }


    //获取指定Cookie名称的Cookie值
    function GetCookie (name) {
        var arg = name + "=";
        var alen = arg.length;
        var clen = document.cookie.length;
        var i = 0;
        while (i < clen) {
            var j = i + alen;
            if (document.cookie.substring(i, j) == arg)
                return getCookieVal (j);
            i = document.cookie.indexOf(" ", i) + 1;
            if (i == 0) break;
        }
        return null;
    }

    function getCookieVal (offset) {
        var endstr = document.cookie.indexOf (";", offset);
        if (endstr == -1)
            endstr = document.cookie.length;
        return unescape(document.cookie.substring(offset, endstr));
    }


    //删除指定名称的cookie
    function delCookie(name){
        var exp = new Date();
        exp.setTime(exp.getTime() - 1);
        var cval=GetCookie(name);
        if(cval!=null) document.cookie= name + "="+cval+";expires="+exp.toGMTString();
    }

    // 自动填充用户
    function AutoLogin(){
        if((GetCookie ("adCode")!=null)&&(GetCookie ("adCode")!="")){
            document.getElementById("adCode").value = GetCookie ("adCode");
            document.getElementById("saveAdCode").checked = true;
        }
        if((GetCookie ("pwd")!=null) &&(GetCookie ("pwd")!="")){
            document.getElementById("pwd").value= GetCookie ("pwd");
            document.getElementById("savePwd").checked = true;
        }
    }



    function imageSubmit() {

            if (document.getElementById("saveAdCode").checked) {
                setCookieCode();
            } else {
                delCookie("adCode");
            }
            if (document.getElementById("savePwd").checked) {
                setCookiePwd();
            } else {
                delCookie("pwd");
            }
    }


        $(function(){

        AutoLogin();
	})

	</script>
	<script type="text/javascript"
			src="/UALogin/js/mix/smix.js">
	</script>

	<script type="text/javascript"
			src="/UALogin/js/security.js">
	</script>
	<script type="text/javascript"
			src="/UALogin/js/UTCserver.js">
	</script>
	<script type="text/javascript"
			src="/UALogin/js/mix/login.js?v=2.0">
	</script>
	<script type="text/javascript"
			src="/UALogin/js/mix/autowrite.js">
	</script>
	
</head>
<body onload="initPageFn();" leftmargin="0" topmargin="0"
	  marginwidth="0" marginheight="0">
<script>

	function doKeyPress(evt){
		evt=evt?evt:window.event;
		if (evt.keyCode==13) {
			document.forms[0]["pwd"].focus();
			if (evt.preventDefault) evt.preventDefault();
			evt.returnValue=false;
		}
	}
	function doKeyPressToAuthCode(evt){
		evt=evt?evt:window.event;
		if (evt.keyCode==13) {
			document.forms[0]["authCode"].focus();
			if (evt.preventDefault) evt.preventDefault();
			evt.returnValue=false;
		}
	}
	function reloadImage(url)
	{
		document.getElementById('safecode').src = url+"?id=" +(new Date()).getTime();
	function patternScript(obj)
	{   var s =obj.value;
		var pattern = new RegExp("[%\"`~!@#$^&*()=|{}':;',\\[\\]<>/?~！@#￥……&*（）——|{}【】‘；：”“'。，、？]")   
		var rs = "";
		for (var i = 0; i < s.length; i++) {
			rs = rs+s.substr(i, 1).replace(pattern, '');
		}
		if(rs.length >30)
			rs =rs.substr(0, 30)
		obj.value= rs;
	}
	function loginOption() {
		var dlfs = document.getElementById("dlsf").value
		document.getElementById("loginFlag").value = dlfs
		dlfsParam = dlfs
	//	console.log(111,dlfsParam,dlfsParam=='5')
		$("#yzmField").css({"display":"none"})
		$("#certList").css({"display":"none"})
		$("#pwdField").css({"display":""})
		$("#ddd").css({"height":"8px"})
		if(dlfs==2){
			$("#ddd").css({"height":"8px"})
			faceLogin()
		}else if(dlfs==3){
			$("#ddd").css({"height":"8px"})
			FingerprintLogin()
		}else if(dlfs==4){
			// ukey style="height:38px;"
			$("#certList").css({"display":""})
			$("#ddd").css({"height":"38px"})
			// 证书ID
			var certId = $("#certField").val();
			if (certId == "") {
				alert("未识别到ukey!");
				return;
			}
			$("#ukeySN").val(certId.split("/")[1]);

			// 真随机数
			var plain = "";
			SOF_GenRandom(42, function(retObj) {
				if (retObj.retVal == "") {
					alert("产生随机数失败!");
					return;
				}
				plain = retObj.retVal;
				$("#reqDoc").val(plain);
				// 签名
				var isDetach = 0;
				var doFunc = SOF_SignMessageBase64;
				doFunc(isDetach, certId, plain, function (retObj) {
					console.log("=========================PKCS7签名返回值：");
					console.log(retObj);
					if (retObj.retVal == "") {
						alert("PKCS7签名失败!");
						return;
					}
					// 签名值（P7签名结果）
					$("#signValue").val(retObj.retVal);
				});
			});

		}else if(dlfs==5){
			// 验证码
			$("#ddd").css({"height":"8px"})
			$("#yzmField").css({"display":""})
			$("#pwdField").css({"display":"none"})
		}
	}

	function sendVerification() {
		var nameCode = document.forms[0].adCode.value;
		if (nameCode === '' || nameCode === null) {
			$("#tipField").text("登录名不能为空！")
			return;
		}

		// 获取验证码按钮置为不可用
		$("#verificationField").prop("disabled", "disabled");

		// 定时期，倒计时
		var seconds = 60;
		$("#verificationField").val("获取验证码"+seconds+"s");
		var timer = setInterval(function() {
			seconds--;
			$("#verificationField").val("获取验证码"+seconds+"s");
			if (seconds <= 0 ) {
				clearInterval(timer);
				$("#verificationField").val("获取验证码");
				$("#verificationField").removeProp("disabled");
				$("#tipField").text("")
			}
		}, 1000);

		$.ajax( {
			type: "POST",
			url: "./oauth2/sendVerification",
			data: {nameCode: nameCode},
			async: true,
			success: function(result) {
				if (result.success) {
					$("#verificationId").val(result.data);
				}
				$("#tipField").text(result.message);
			},
			error: function(XMLHttpRequest, textStatus, errorThrown) {
			}
		});
	}
	function faceLogin() {
		if (document.forms[0].adCode.value === '' || document.forms[0].adCode.value === null) {
			alert("登录名不能为空")
			document.getElementById("dlsf").value = 1
			return
		}
		localStorage.setItem('TARGETURL',"xx/appframe-web/welcome.jsp")
		localStorage.setItem('TRAGEURLCODE',"A1F67DA67BFB56B0A516B10BA688D4DC43761E1520339A389C2F685AA7733753679B6829676465261DAD96A4E90EF0A60C6D3D14E5F3BE79")
		localStorage.setItem('adCode',document.forms[0].adCode.value)
		localStorage.setItem('loginFlag',document.getElementById("loginFlag").value)
		localStorage.setItem('URLParam', location.protocol + "//" + location.hostname + ":" + location.port+"/UALogin")
		// window.open("face.html","newWindow","height=600,width=830,left=100,toolbar=no,location=no,menubar=no,titlebar=no")
		layui.use('layer', function () {
			layer.open({
				type: 2,
				content: ["face.html"],   //添加页，后台controller转发到指定页
				area: ["800px", "530px"],
				title: ['面容登录'],
				fixed: false,
				//maxmin: true,
				shadeClose: true,
				time: 30000,
				end: function () {  //刷新页面
					document.getElementById("dlsf").value = 1
					document.getElementById("tipField").textContent = "登录失败,请重试！";
				}
			})
		})
	}
	function FingerprintLogin() {
		if (document.forms[0].adCode.value === '' || document.forms[0].adCode.value === null) {
			//alert("登录名不能为空")
			document.getElementById("dlsf").value = 1
			return
		}
		localStorage.setItem('TARGETURL',"xx/appframe-web/welcome.jsp")
		localStorage.setItem('TRAGEURLCODE',"A1F67DA67BFB56B0A516B10BA688D4DC43761E1520339A389C2F685AA7733753679B6829676465261DAD96A4E90EF0A60C6D3D14E5F3BE79")
		localStorage.setItem('adCode',document.forms[0].adCode.value)
		localStorage.setItem('loginFlag',document.getElementById("loginFlag").value)
		localStorage.setItem('URLParam',location.protocol + "//"  + location.hostname + ":" + location.port+"/UALogin")
		layui.use('layer', function () {
			layer.open({
				type: 2,
				content: ["fingerPrint.html"],   //添加页，后台controller转发到指定页
				area: ["800px", "530px"],
				title: ['指纹登录'],
				fixed: false,
				//maxmin: true,
				shadeClose: true,
				end: function () {  //刷新页面
					document.getElementById("dlsf").value = 1;
				}
			})
		})
	}

</script>
<p>
	<!-- End ImageReady Slices -->
	<br> <br> <br> <br> <br> <br>
</p>

<p>&nbsp;</p>

<p>&nbsp;</p>
<!-- 必须项 begin -->
<form method="post" name="UALoginForm" id="UALoginForm"
	  action="/UALogin/UserAuthAuth"
	  onsubmit="doCipherSubmit(true,'010001','00ac8da600bafabc31bdfe9f1f059ffa06ed27c3b4df9926f0203c5daf4ffe00970e67881d70bf3fb32c82ea0d1084df828c0334427dfa4f749adcb389770a76a03e3b715216e7a6c19d48ed964c3b9d5c80ba8347d8f708c8b8a59a3838b2133bd39144dbd2d10102a5a5a310faaa8b0cad9aa13543578cd4392e0b535464ba93','encryptedAdCode');return false;">
	<!-- 必须项 end -->
	<table width="100%" height="130" border="0" cellpadding="0"
		   cellspacing="0">
		<tr>
			<td width="16%" background="/UALogin/image/login_02.jpg"><img
					src="/UALogin/image/login_02.jpg" width="23" class="imgp" height="320" alt="q"></td>
			<td width="83%" background="/UALogin/image/login_02.jpg">
				<table width="852" height="212" border="0" cellpadding="0"
					   cellspacing="0">

					<tr>
						
						<td height="320" background="/UALogin/image/login_06.gif">
							
							<table width="100%" height="212" border="0" cellpadding="0"
								   cellspacing="0">
								<tr>
									<td width="49%"><span class="STYLE1"></span></td>
									<td width="51%" valign="bottom">
										<table width="100%" height="88" border="0"  style="margin-top: 45px" cellpadding="0"
											   cellspacing="0">
											<tr>
												<td>
													<table width="100%" border="0" cellspacing="3"
														   cellpadding="2">
														<!--必须项 begin ，其中的属性name,id,value保留-->
														<tr>
															<td height="45">
																	
														</td>
														</tr>

														<tr id="certList" style="display: none;">
															<td height="20">
																<font size="2">证　书</font>
																<select class="StandardWidth" id="certField">
																</select>
															</td>

															
															<input name="reqDoc" style="display: none;"
																   tabindex="4" type="text" id="reqDoc">
															
															<input name="signValue" style="display: none;"
																   tabindex="4" type="text" id="signValue">
															
															<input name="ukeySN" style="display: none;"
																   tabindex="4" type="text" id="ukeySN">
														</tr>

														<tr style="margin-top:35px;padding-top:71px;height:38px" >
															<td width="86%"><font size="2">登录名</font> <input
																	name="adCode"
																	tabindex="1" type="text" class="input1"
																	id="adCode" value=""
																	onkeypress="doKeyPress(event);"
																	onblur="patternScript(this);" size="14">
																<label style="font-size: 13px"><input type="checkbox" name="saveAdCode"  id="saveAdCode" value=""/>保存用户 </label> </td>
														</tr>

														<tr>
															<td width="86%"  id="pwdField"><font size="2">密　码</font> <input name="pwd"
																											 tabindex="2" type="password" class="input1" id="pwd"
																
																											 size="14">
																<label style="font-size: 13px"><input type="checkbox" name="savePwd" id="savePwd" value="" />保存密码</label> </td>
															</td>
														</tr>

															<tr>
																<td  id="yzmField" style="display: none;">
																	<font size="2">验证码</font>
																	<input name="verificationValue" tabindex="3" type="text" class="input1" id="verificationValue" size="14">
																	<input style="font-size: 12px" type="button" value="获取验证码" name="verificationField" id="verificationField" onclick="sendVerification()">
																</td>
																<input name="verificationId" style="display: none;"
																	   tabindex="4" type="text" id="verificationId">
															</tr>

														
														<tr>
															<td><font size="2">&nbsp;</font>  <!--必须项 end -->
														<tr>
															<td height="19">
																<table width="55%" border="0" cellspacing="0"
																	   cellpadding="0">
																	<tr>
																		<td width="0%"> </td>
																		<td width="25%"><input type="image"
																							   name="imageField" id="imageField"
																							   src="/UALogin/image/anq.gif" alt="登 录"></td>
																		<td width="25%"><img name="imageField2"
																							 style="cursor: pointer;" id="imageField2" alt="取 消"
																							 onclick="document.forms[0].reset();"
																							 src="/UALogin/image/an_14.gif"></td>
																		

																		<td width="25%"><a href="oauth2/forgetSecret"
																						   style="cursor: pointer; font-size: 12px; color: black; text-decoration: none"
																						   target="_blank">忘记密码</a></td>
																		
																		<td width="25%" ><a href="http://iscmp.sgcc.com.cn/isc_mp_auth/ownregister/regist/reg.jsp?prvid=EC11DD6358CF7D72E0430100007F22C6"
																							style="cursor: pointer; font-size: 12px; color: black; text-decoration: none"
																							target="_blank">用户注册</a></td>
																	</tr>
																</table>
															</td>
														</tr>
													</table>
												</td>
											</tr>
										</table>
									</td>
								</tr>
							</table>
						</td>
					</tr>
				</table>
			</td>
			<td width="1%" background="/UALogin/image/login_02.jpg">&nbsp;</td>
		</tr>
		<tr>
			<td colspan="3" nowrap align="center" height="20">
				<!-- 必须项 begin errorMessage需要有一个显示的地方--> <font id="tipField" color="#4d6d8b" size="2">
				
			</font> <!-- 必须项 end -->
			</td>
		</tr>
	</table>

	<!-- 必须项 begin -->
	<input name="TRAGEURLCODE"
		   type="hidden" id="TRAGEURLCODE"
		   value="A1F67DA67BFB56B0A516B10BA688D4DC43761E1520339A389C2F685AA7733753679B6829676465261DAD96A4E90EF0A60C6D3D14E5F3BE79" />
	<!-- 当为AG模式时，USERID 和SAPUSERID是必须的属性  begin -->
	<input name="userId" type="hidden"
		   id="userId" value=""> <input
		name="userSourceId" type="hidden"
		id="userSourceId" value="">
	<input name="jsIdsDomain" type="hidden"
		   id="jsIdsDomain" value="">
	<input name="saltPwd" type="hidden" id="saltPwd" value="">
	<input name="tsl" type="hidden" id="tsl" value="">
	<input name="nonce" type="hidden" id="nonce" value="">
	<input name="encryptedAdCode" type="hidden"
		   id="encryptedAdCode" value="">
	<input name="loginFlag" type="hidden"
		   id="loginFlag" value="">
	<input name="authFile" type="hidden"
		   id="authFile" value="">
	<!-- 当为AG模式时，USERID 和SAPUSERID是必须的属性  end -->
	
	<!-- 必须项 end -->
</form>

<p>&nbsp;</p>
</body>
</html>